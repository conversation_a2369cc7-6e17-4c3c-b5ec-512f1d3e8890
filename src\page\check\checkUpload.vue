<template>
<div class="upload" v-if="show">
	<div class="mask" @click="close"></div>
	<div class="content">
		<div class="btn">
			<div class="button" @click="uploadVideo">视频</div>
			<div class="button" @click="uploadImg">图片</div>
		</div>
	</div>
</div>
</template>
<script>
import { selectVideo } from '@/assets/js/until.js';
import { upload, getFileToken } from '../../api/check';
import lrz from 'lrz';

export default {
	data () {
		return {
			show: false
		};
	},
	mounted () {},
	methods: {
		async uploadVideo () {
			const _this = this;
			_this.close();

			try {
				// 使用H5选择视频
				const videoData = await selectVideo();
				console.log('选择视频成功', videoData);

				_this.$loading(true, '正在上传视频...');

				// 获取上传token
				const token = await getFileToken();
				if (token.errorCode.toString() !== "0") {
					_this.$toast.center(token.value || '获取上传凭证失败');
					_this.$loading(false);
					return;
				}

				// 构建上传FormData
				const formData = new FormData();
				formData.append('file', videoData.file);
				formData.append('fileToken', token.data);

				// 上传视频（视频使用专门的上传接口）
				let saveUrl = window.location.origin + "/medical-supervision/file/uploadVedio";
				const response = await fetch(saveUrl, {
					method: 'POST',
					body: formData
				});

				const result = await response.json();

				if (result.errorCode === '0' || result.errorCode === 0) {
					if (!result.data || !result.data.fileInfo) {
						_this.$toast.center('视频上传失败：服务器返回数据无效');
						_this.$loading(false);
						return;
					}

					const fileInfo = result.data.fileInfo;
					_this.$loading(false);
					_this.$toast.center('上传视频成功');
					_this.$store.commit('setUploadImgs', {
						type: 'video',
						url: fileInfo.fileUrl,
						fileId: fileInfo.fileId
					});
				} else {
					_this.$loading(false);
					_this.$toast.center(result.value || '上传视频失败');
				}
			} catch (error) {
				console.error('视频选择或上传失败', error);
				_this.$loading(false);
				if (error.message !== '用户取消选择') {
					_this.$toast.center('上传视频失败');
				}
			}
		},
		uploadImg () {
			const _this = this;
			_this.close();

			// H5图片选择
			const input = document.createElement('input');
			input.type = 'file';
			input.accept = 'image/*';

			input.onchange = async (e) => {
				const file = e.target.files[0];
				if (!file) return;

				try {
					_this.$loading(true, '正在上传图片...');

					// 使用 lrz 压缩图片
					const rst = await lrz(file, {
						width: 1000,
						quality: 0.5,
					});

					// 获取上传token
					const token = await getFileToken();
					if (token.errorCode.toString() !== "0") {
						_this.$toast.center(token.value || '获取上传凭证失败');
						_this.$loading(false);
						return;
					}

					const fd = new FormData();
					fd.append("file", file);
					fd.append("fileToken", token.data);

					const res = await upload(fd);

					if (res.errorCode.toString() === "0") {
						_this.$loading(false);
						_this.$toast.center('上传图片成功');
						_this.$store.commit('setUploadImgs', {
							type: 'img',
							url: res.data.fileUrl,
							fileId: res.data.fileId
						});
					} else {
						_this.$loading(false);
						_this.$toast.center(res.value || '上传图片失败');
					}
				} catch (error) {
					console.error('图片上传失败', error);
					_this.$loading(false);
					_this.$toast.center('上传图片失败');
				}
			};

			input.oncancel = () => {
				console.log('用户取消选择图片');
			};

			input.click();
		},
		open () {
			this.show = true;
		},
		close () {
			this.show = false;
		}
	},
	components: {},
	watch: {
		show (nv, ov) {
			if (nv) {
				// console.log('nv', nv);
				this.$store.commit('setHide', true)
			} else {
				this.$store.commit('setHide', false)
			}
		}
	}
};
</script>
<style lang="less" scoped>
.upload{
	position: fixed;
	top: 0;
	bottom: 1px;
	left: 0;
	right: 0;
	z-index: 100;
	.mask{
		position: relative;
		width: 100%;
		height: 100%;
		z-index: 101;
		background: rgba(0, 0, 0, 0.5)
	}
	.content{
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 500px;
		background: #fff;
		z-index: 102;
		.btn{
			display: flex;
			height: 100px;
			line-height: 100px;
			.button{
				flex: 1;
				text-align: center;
				height: 100%;
				background: #f7f7f7;
				&:nth-child(1){
					border-right: 1px solid #fff;
				}
			}
		}
	}
}
</style>
